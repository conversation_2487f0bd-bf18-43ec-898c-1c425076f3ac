D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\java\com\example\myapplication\HealthDietUI.kt:264: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        Text("BMI指数: ${String.format("%.1f", bmi)} (${userInfo.getBMICategory()})")
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:18: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

D:\Users\Lenovo\AndroidStudioProjects\MyApplication\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.13.1 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\Users\Lenovo\AndroidStudioProjects\MyApplication\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.8.3 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
D:\Users\Lenovo\AndroidStudioProjects\MyApplication\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.8.2 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

0 errors, 12 warnings
