1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:5:5-26:19
18        android:allowBackup="true"
18-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:7:9-65
21        android:extractNativeLibs="false"
22        android:fullBackupContent="@xml/backup_rules"
22-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:8:9-54
23        android:icon="@mipmap/ic_launcher"
23-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:9:9-43
24        android:label="@string/app_name"
24-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:10:9-41
25        android:roundIcon="@mipmap/ic_launcher_round"
25-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:11:9-54
26        android:supportsRtl="true"
26-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:12:9-35
27        android:theme="@style/Theme.MyApplication" >
27-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:13:9-51
28        <activity
28-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:15:9-25:20
29            android:name="com.example.myapplication.MainActivity"
29-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:16:13-41
30            android:exported="true"
30-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:17:13-36
31            android:label="@string/app_name"
31-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:18:13-45
32            android:theme="@style/Theme.MyApplication" >
32-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:19:13-55
33            <intent-filter>
33-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:20:13-24:29
34                <action android:name="android.intent.action.MAIN" />
34-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:21:17-69
34-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:21:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:23:17-77
36-->D:\Users\Lenovo\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:23:27-74
37            </intent-filter>
38        </activity>
39
40        <provider
40-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
41            android:name="androidx.startup.InitializationProvider"
41-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
42            android:authorities="com.example.myapplication.androidx-startup"
42-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
43            android:exported="false" >
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
44            <meta-data
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
45                android:name="androidx.emoji2.text.EmojiCompatInitializer"
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
46                android:value="androidx.startup" />
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
47            <meta-data
47-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
48-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
49                android:value="androidx.startup" />
49-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
51-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
52                android:value="androidx.startup" />
52-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
53        </provider>
54
55        <receiver
55-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
56            android:name="androidx.profileinstaller.ProfileInstallReceiver"
56-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
57            android:directBootAware="false"
57-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
58            android:enabled="true"
58-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
59            android:exported="true"
59-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
60            android:permission="android.permission.DUMP" >
60-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
61            <intent-filter>
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
62                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
63            </intent-filter>
64            <intent-filter>
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
65                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
66            </intent-filter>
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
68                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
69            </intent-filter>
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
71                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
72            </intent-filter>
73        </receiver>
74    </application>
75
76</manifest>
