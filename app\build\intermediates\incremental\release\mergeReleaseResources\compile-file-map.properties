#Sun Jun 08 17:55:48 CST 2025
com.example.myapplication.app-main-43\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.myapplication.app-main-43\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-43\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.myapplication.app-main-43\:/mipmap-mdpi/ic_launcher.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-43\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.myapplication.app-main-43\:/mipmap-xhdpi/ic_launcher.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-43\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.myapplication.app-main-43\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.myapplication.app-main-43\:/drawable/ic_launcher_background.xml=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_background.xml.flat
com.example.myapplication.app-main-43\:/mipmap-hdpi/ic_launcher.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-43\:/drawable/ic_launcher_foreground.xml=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_foreground.xml.flat
com.example.myapplication.app-main-43\:/xml/data_extraction_rules.xml=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_data_extraction_rules.xml.flat
com.example.myapplication.app-main-43\:/xml/backup_rules.xml=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_backup_rules.xml.flat
com.example.myapplication.app-main-43\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-43\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.myapplication.app-main-43\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\Users\\Lenovo\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
