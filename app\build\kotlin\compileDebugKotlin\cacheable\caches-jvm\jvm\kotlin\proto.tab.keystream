   + c o m / e x a m p l e / m y a p p l i c a t i o n / D i e t P l a n G e n e r a t o r   ( c o m / e x a m p l e / m y a p p l i c a t i o n / U s e r H e a l t h I n f o   " c o m / e x a m p l e / m y a p p l i c a t i o n / D i e t P l a n   ( c o m / e x a m p l e / m y a p p l i c a t i o n / H e a l t h D i e t U I K t   & c o m / e x a m p l e / m y a p p l i c a t i o n / M a i n A c t i v i t y   * c o m / e x a m p l e / m y a p p l i c a t i o n / u i / t h e m e / C o l o r K t   * c o m / e x a m p l e / m y a p p l i c a t i o n / u i / t h e m e / T h e m e K t   ) c o m / e x a m p l e / m y a p p l i c a t i o n / u i / t h e m e / T y p e K t    . k o t l i n _ m o d u l e                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                