{"logs": [{"outputFile": "com.example.myapplication.app-mergeReleaseResources-40:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ffaf60804bb777caaab07cd66703845d\\transformed\\ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,987,1068,1140,1215,1290,1365,1445,1511", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,982,1063,1135,1210,1285,1360,1440,1506,1624"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "838,931,1014,1106,1205,1293,1371,7653,7741,7825,7904,7985,8057,8132,8207,8383,8463,8529", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "926,1009,1101,1200,1288,1366,1464,7736,7820,7899,7980,8052,8127,8202,8277,8458,8524,8642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee81e9003baef06be7850ff4f00325b8\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,408,509,611,709,8282", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "198,300,403,504,606,704,833,8378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8b176e8aba06099230d4846cfebebfa3\\transformed\\material3-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,291,407,526,623,724,842,980,1104,1247,1332,1435,1525,1622,1734,1855,1963,2098,2235,2366,2532,2658,2773,2892,3012,3103,3199,3318,3454,3556,3659,3765,3897,4035,4146,4245,4321,4418,4519,4631,4716,4804,4903,4983,5067,5167,5266,5361,5459,5545,5646,5744,5846,5961,6041,6143", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "167,286,402,521,618,719,837,975,1099,1242,1327,1430,1520,1617,1729,1850,1958,2093,2230,2361,2527,2653,2768,2887,3007,3098,3194,3313,3449,3551,3654,3760,3892,4030,4141,4240,4316,4413,4514,4626,4711,4799,4898,4978,5062,5162,5261,5356,5454,5540,5641,5739,5841,5956,6036,6138,6234"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1469,1586,1705,1821,1940,2037,2138,2256,2394,2518,2661,2746,2849,2939,3036,3148,3269,3377,3512,3649,3780,3946,4072,4187,4306,4426,4517,4613,4732,4868,4970,5073,5179,5311,5449,5560,5659,5735,5832,5933,6045,6130,6218,6317,6397,6481,6581,6680,6775,6873,6959,7060,7158,7260,7375,7455,7557", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "1581,1700,1816,1935,2032,2133,2251,2389,2513,2656,2741,2844,2934,3031,3143,3264,3372,3507,3644,3775,3941,4067,4182,4301,4421,4512,4608,4727,4863,4965,5068,5174,5306,5444,5555,5654,5730,5827,5928,6040,6125,6213,6312,6392,6476,6576,6675,6770,6868,6954,7055,7153,7255,7370,7450,7552,7648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fce910b49377672473080ebeba046646\\transformed\\foundation-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8647,8734", "endColumns": "86,86", "endOffsets": "8729,8816"}}]}]}