package com.example.myapplication

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.myapplication.ui.theme.MyApplicationTheme

data class UserHealthInfo(
    // 1. 基础病情数据
    var diseaseType: String = "",           // 疾病类型及严重程度
    var currentMedication: String = "",     // 当前用药情况

    // 2. 身体基础指标
    var height: String = "",                // 身高
    var weight: String = "",                // 体重
    var waistCircumference: String = "",    // 腰围
    var hipCircumference: String = "",      // 臀围
    var bloodTestResults: String = "",      // 血糖/血脂检测值

    // 3. 饮食与生活习惯
    var dailyActivityLevel: String = "",    // 日常活动量
    var foodAllergies: String = "",         // 食物过敏史/禁忌
    var existingDietHabits: String = ""     // 现有饮食习惯
) {
    fun calculateBMI(): Double? {
        val heightInM = height.toDoubleOrNull()?.div(100)
        val weightInKg = weight.toDoubleOrNull()
        return if (heightInM != null && weightInKg != null && heightInM > 0) {
            weightInKg / (heightInM * heightInM)
        } else null
    }

    fun getBMICategory(): String {
        val bmi = calculateBMI()
        return when {
            bmi == null -> "无法计算"
            bmi < 18.5 -> "偏瘦"
            bmi < 24 -> "正常"
            bmi < 28 -> "超重"
            else -> "肥胖"
        }
    }
}

// 食谱数据类
data class DietPlan(
    val breakfast: List<String>,
    val lunch: List<String>,
    val dinner: List<String>,
    val snacks: List<String>,
    val recommendations: List<String>,
    val warnings: List<String>
)

@Composable
fun HealthInfoInputScreen(onGeneratePlan: (UserHealthInfo) -> Unit) {
    var userHealthInfo by remember { mutableStateOf(UserHealthInfo()) }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        item {
            Text(
                text = "🍽️ 智能食谱",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Text(
                text = "基于您的健康状况，AI为您量身定制专属营养方案",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.diseaseType,
                onValueChange = { userHealthInfo = userHealthInfo.copy(diseaseType = it) },
                label = { Text("疾病类型及严重程度") },
                placeholder = { Text("如：2型糖尿病（血糖控制良好）、脂肪肝（轻度）、高血压") },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.currentMedication,
                onValueChange = { userHealthInfo = userHealthInfo.copy(currentMedication = it) },
                label = { Text("当前用药情况") },
                placeholder = { Text("如：胰岛素每日20单位、二甲双胍500mg每日两次") },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.height,
                onValueChange = { userHealthInfo = userHealthInfo.copy(height = it) },
                label = { Text("身高 (cm)") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.weight,
                onValueChange = { userHealthInfo = userHealthInfo.copy(weight = it) },
                label = { Text("体重 (kg)") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.waistCircumference,
                onValueChange = { userHealthInfo = userHealthInfo.copy(waistCircumference = it) },
                label = { Text("腰围 (cm)") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.hipCircumference,
                onValueChange = { userHealthInfo = userHealthInfo.copy(hipCircumference = it) },
                label = { Text("臀围 (cm)") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.bloodTestResults,
                onValueChange = { userHealthInfo = userHealthInfo.copy(bloodTestResults = it) },
                label = { Text("血糖/血脂检测值") },
                placeholder = { Text("如：空腹血糖5.6、餐后血糖7.8、糖化血红蛋白6.5%、总胆固醇4.5") },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.dailyActivityLevel,
                onValueChange = { userHealthInfo = userHealthInfo.copy(dailyActivityLevel = it) },
                label = { Text("日常活动量") },
                placeholder = { Text("如：办公室久坐、轻体力劳动、中等强度运动、高强度训练") },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.foodAllergies,
                onValueChange = { userHealthInfo = userHealthInfo.copy(foodAllergies = it) },
                label = { Text("食物过敏史/禁忌") },
                placeholder = { Text("如：乳糖不耐受、海鲜过敏、坚果过敏、素食主义") },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            OutlinedTextField(
                value = userHealthInfo.existingDietHabits,
                onValueChange = { userHealthInfo = userHealthInfo.copy(existingDietHabits = it) },
                label = { Text("现有饮食习惯") },
                placeholder = { Text("如：喜甜食、口味偏咸、爱吃油炸食品、进食时间不规律") },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            Button(
                onClick = { onGeneratePlan(userHealthInfo) },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("🤖 AI生成智能食谱")
            }
        }
    }
}

@Composable
fun DietPlanScreen(dietPlan: DietPlan, userInfo: UserHealthInfo, onBack: () -> Unit) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "🤖 AI智能食谱",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
                Button(onClick = onBack) {
                    Text("返回修改")
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        // 显示BMI信息
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.primaryContainer)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "📊 健康指标分析",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    val bmi = userInfo.calculateBMI()
                    if (bmi != null) {
                        Text("BMI指数: ${String.format("%.1f", bmi)} (${userInfo.getBMICategory()})")
                        Text("建议: 根据您的BMI指数，为您定制了专门的营养方案")
                    } else {
                        Text("请完善身高体重信息以获得更准确的建议")
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        // 早餐
        item {
            MealSection("🌅 早餐建议", dietPlan.breakfast)
            Spacer(modifier = Modifier.height(12.dp))
        }

        // 午餐
        item {
            MealSection("🌞 午餐建议", dietPlan.lunch)
            Spacer(modifier = Modifier.height(12.dp))
        }

        // 晚餐
        item {
            MealSection("🌙 晚餐建议", dietPlan.dinner)
            Spacer(modifier = Modifier.height(12.dp))
        }

        // 零食
        if (dietPlan.snacks.isNotEmpty()) {
            item {
                MealSection("🍎 健康零食", dietPlan.snacks)
                Spacer(modifier = Modifier.height(12.dp))
            }
        }

        // 营养建议
        if (dietPlan.recommendations.isNotEmpty()) {
            item {
                RecommendationSection("💡 专业营养建议", dietPlan.recommendations, MaterialTheme.colorScheme.secondaryContainer)
                Spacer(modifier = Modifier.height(12.dp))
            }
        }

        // 注意事项
        if (dietPlan.warnings.isNotEmpty()) {
            item {
                RecommendationSection("⚠️ 重要注意事项", dietPlan.warnings, MaterialTheme.colorScheme.errorContainer)
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
    }
}

@Composable
fun MealSection(title: String, items: List<String>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            items.forEachIndexed { index, item ->
                Text(
                    text = "${index + 1}. $item",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}

@Composable
fun RecommendationSection(title: String, items: List<String>, backgroundColor: androidx.compose.ui.graphics.Color) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            items.forEachIndexed { index, item ->
                Text(
                    text = "${index + 1}. $item",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}

@Composable
fun HealthDietApp() {
    var currentScreen by remember { mutableStateOf("input") }
    var currentUserInfo by remember { mutableStateOf(UserHealthInfo()) }
    var currentDietPlan by remember { mutableStateOf<DietPlan?>(null) }

    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        Box(modifier = Modifier.padding(innerPadding)) {
            when (currentScreen) {
                "input" -> {
                    HealthInfoInputScreen(
                        onGeneratePlan = { userInfo ->
                            currentUserInfo = userInfo
                            currentDietPlan = DietPlanGenerator.generateDietPlan(userInfo)
                            currentScreen = "result"
                        }
                    )
                }
                "result" -> {
                    currentDietPlan?.let { dietPlan ->
                        DietPlanScreen(
                            dietPlan = dietPlan,
                            userInfo = currentUserInfo,
                            onBack = { currentScreen = "input" }
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HealthInfoInputScreenPreview() {
    MyApplicationTheme {
        HealthInfoInputScreen(onGeneratePlan = {})
    }
}
