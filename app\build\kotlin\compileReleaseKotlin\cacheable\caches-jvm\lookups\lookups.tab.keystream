  Activity android.app  
HealthDietApp android.app.Activity  MyApplicationTheme android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  Context android.content  
HealthDietApp android.content.Context  MyApplicationTheme android.content.Context  enableEdgeToEdge android.content.Context  
setContent android.content.Context  
HealthDietApp android.content.ContextWrapper  MyApplicationTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
HealthDietApp  android.view.ContextThemeWrapper  MyApplicationTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
HealthDietApp #androidx.activity.ComponentActivity  MyApplicationTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Column .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  forEachIndexed .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  Button +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  Arrangement .androidx.compose.foundation.lazy.LazyItemScope  Button .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  KeyboardOptions .androidx.compose.foundation.lazy.LazyItemScope  KeyboardType .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  MealSection .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  OutlinedTextField .androidx.compose.foundation.lazy.LazyItemScope  RecommendationSection .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  String .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  
cardColors .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  format .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  Arrangement .androidx.compose.foundation.lazy.LazyListScope  Button .androidx.compose.foundation.lazy.LazyListScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  KeyboardOptions .androidx.compose.foundation.lazy.LazyListScope  KeyboardType .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  MealSection .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  OutlinedTextField .androidx.compose.foundation.lazy.LazyListScope  RecommendationSection .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  String .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  
cardColors .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  format .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  KeyboardOptions  androidx.compose.foundation.text  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  OutlinedTextField androidx.compose.material3  Scaffold androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  errorContainer &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  secondaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
Composable androidx.compose.runtime  MutableState androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  Modifier androidx.compose.ui  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Number +androidx.compose.ui.text.input.KeyboardType  Number 5androidx.compose.ui.text.input.KeyboardType.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
HealthDietApp #androidx.core.app.ComponentActivity  MyApplicationTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  Arrangement com.example.myapplication  Bundle com.example.myapplication  Button com.example.myapplication  Card com.example.myapplication  CardDefaults com.example.myapplication  Column com.example.myapplication  ComponentActivity com.example.myapplication  
Composable com.example.myapplication  DietPlan com.example.myapplication  DietPlanGenerator com.example.myapplication  DietPlanScreen com.example.myapplication  Double com.example.myapplication  
FontWeight com.example.myapplication  
HealthDietApp com.example.myapplication  HealthInfoInputScreen com.example.myapplication  HealthInfoInputScreenPreview com.example.myapplication  KeyboardOptions com.example.myapplication  KeyboardType com.example.myapplication  List com.example.myapplication  MainActivity com.example.myapplication  
MaterialTheme com.example.myapplication  MealSection com.example.myapplication  Modifier com.example.myapplication  MyApplicationTheme com.example.myapplication  OutlinedTextField com.example.myapplication  Preview com.example.myapplication  RecommendationSection com.example.myapplication  Row com.example.myapplication  Spacer com.example.myapplication  String com.example.myapplication  Text com.example.myapplication  Unit com.example.myapplication  UserHealthInfo com.example.myapplication  androidx com.example.myapplication  
cardColors com.example.myapplication  contains com.example.myapplication  fillMaxSize com.example.myapplication  fillMaxWidth com.example.myapplication  forEachIndexed com.example.myapplication  format com.example.myapplication  generateDietPlan com.example.myapplication  height com.example.myapplication  
isNotEmpty com.example.myapplication  let com.example.myapplication  listOf com.example.myapplication  
mutableListOf com.example.myapplication  padding com.example.myapplication  provideDelegate com.example.myapplication  replace com.example.myapplication  toDoubleOrNull com.example.myapplication  	breakfast "com.example.myapplication.DietPlan  dinner "com.example.myapplication.DietPlan  let "com.example.myapplication.DietPlan  lunch "com.example.myapplication.DietPlan  recommendations "com.example.myapplication.DietPlan  snacks "com.example.myapplication.DietPlan  warnings "com.example.myapplication.DietPlan  DietPlan +com.example.myapplication.DietPlanGenerator  contains +com.example.myapplication.DietPlanGenerator  generateDietPlan +com.example.myapplication.DietPlanGenerator  listOf +com.example.myapplication.DietPlanGenerator  
mutableListOf +com.example.myapplication.DietPlanGenerator  replace +com.example.myapplication.DietPlanGenerator  
HealthDietApp &com.example.myapplication.MainActivity  MyApplicationTheme &com.example.myapplication.MainActivity  enableEdgeToEdge &com.example.myapplication.MainActivity  
setContent &com.example.myapplication.MainActivity  bloodTestResults (com.example.myapplication.UserHealthInfo  calculateBMI (com.example.myapplication.UserHealthInfo  copy (com.example.myapplication.UserHealthInfo  currentMedication (com.example.myapplication.UserHealthInfo  dailyActivityLevel (com.example.myapplication.UserHealthInfo  diseaseType (com.example.myapplication.UserHealthInfo  existingDietHabits (com.example.myapplication.UserHealthInfo  
foodAllergies (com.example.myapplication.UserHealthInfo  getBMICategory (com.example.myapplication.UserHealthInfo  height (com.example.myapplication.UserHealthInfo  hipCircumference (com.example.myapplication.UserHealthInfo  toDoubleOrNull (com.example.myapplication.UserHealthInfo  waistCircumference (com.example.myapplication.UserHealthInfo  weight (com.example.myapplication.UserHealthInfo  compose "com.example.myapplication.androidx  ui *com.example.myapplication.androidx.compose  graphics -com.example.myapplication.androidx.compose.ui  Color 6com.example.myapplication.androidx.compose.ui.graphics  Boolean "com.example.myapplication.ui.theme  Build "com.example.myapplication.ui.theme  
Composable "com.example.myapplication.ui.theme  DarkColorScheme "com.example.myapplication.ui.theme  
FontFamily "com.example.myapplication.ui.theme  
FontWeight "com.example.myapplication.ui.theme  LightColorScheme "com.example.myapplication.ui.theme  MyApplicationTheme "com.example.myapplication.ui.theme  Pink40 "com.example.myapplication.ui.theme  Pink80 "com.example.myapplication.ui.theme  Purple40 "com.example.myapplication.ui.theme  Purple80 "com.example.myapplication.ui.theme  PurpleGrey40 "com.example.myapplication.ui.theme  PurpleGrey80 "com.example.myapplication.ui.theme  
Typography "com.example.myapplication.ui.theme  Unit "com.example.myapplication.ui.theme  
UnaryOperator java.util.function  <SAM-CONSTRUCTOR>  java.util.function.UnaryOperator  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  String kotlin  let kotlin  	compareTo 
kotlin.Double  div 
kotlin.Double  sp 
kotlin.Double  times 
kotlin.Double  invoke kotlin.Function1  	compareTo 
kotlin.Int  plus 
kotlin.Int  	Companion 
kotlin.String  contains 
kotlin.String  format 
kotlin.String  replace 
kotlin.String  toDoubleOrNull 
kotlin.String  format kotlin.String.Companion  List kotlin.collections  MutableList kotlin.collections  contains kotlin.collections  forEachIndexed kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  forEachIndexed kotlin.collections.List  
isNotEmpty kotlin.collections.List  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  
replaceAll kotlin.collections.MutableList  contains 
kotlin.ranges  KMutableProperty0 kotlin.reflect  contains kotlin.sequences  forEachIndexed kotlin.sequences  contains kotlin.text  forEachIndexed kotlin.text  format kotlin.text  
isNotEmpty kotlin.text  replace kotlin.text  toDoubleOrNull kotlin.text  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  DietPlanGenerator +androidx.compose.foundation.layout.BoxScope  DietPlanScreen +androidx.compose.foundation.layout.BoxScope  HealthInfoInputScreen +androidx.compose.foundation.layout.BoxScope  generateDietPlan +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              