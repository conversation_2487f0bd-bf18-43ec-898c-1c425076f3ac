package com.example.myapplication

// 食谱生成器
object DietPlanGenerator {
    fun generateDietPlan(userInfo: UserHealthInfo): DietPlan {
        val breakfast = mutableListOf<String>()
        val lunch = mutableListOf<String>()
        val dinner = mutableListOf<String>()
        val snacks = mutableListOf<String>()
        val recommendations = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // 基于BMI的建议
        val bmiCategory = userInfo.getBMICategory()
        
        when (bmiCategory) {
            "偏瘦" -> {
                breakfast.addAll(listOf(
                    "燕麦粥（50g燕麦+250ml牛奶）配核桃仁10g",
                    "全麦面包2片配牛油果半个+煎蛋1个",
                    "小米粥1碗+蒸蛋羹+坚果15g",
                    "酸奶200ml+香蕉1根+燕麦片30g"
                ))
                lunch.addAll(listOf(
                    "糙米饭150g+红烧带鱼100g+炒菠菜+紫菜蛋花汤",
                    "五谷杂粮饭120g+清蒸鲈鱼120g+蒜蓉西兰花+冬瓜排骨汤",
                    "藜麦饭100g+香煎三文鱼100g+胡萝卜炒豆角+番茄鸡蛋汤",
                    "糙米饭130g+白切鸡腿肉100g+清炒小白菜+海带豆腐汤"
                ))
                dinner.addAll(listOf(
                    "小米粥1碗+蒸蛋羹+清炒菠菜+坚果5颗",
                    "红薯粥1碗+煎蛋1个+凉拌黄瓜+核桃3个",
                    "南瓜粥1碗+蒸蛋+蒜蓉生菜+杏仁8颗"
                ))
                recommendations.add("每日增加300-500卡路里摄入")
                recommendations.add("适当增加优质蛋白质：鱼肉、鸡蛋、豆制品")
                recommendations.add("健康脂肪来源：坚果、牛油果、深海鱼")
            }
            "正常" -> {
                breakfast.addAll(listOf(
                    "燕麦片40g+脱脂牛奶200ml+蓝莓50g+煮鸡蛋1个",
                    "全麦面包1片+水煮蛋1个+圣女果5个+无糖豆浆250ml",
                    "紫薯100g+酸奶150ml+苹果半个+坚果5颗",
                    "小米粥1碗+蒸蛋白2个+凉拌黄瓜+核桃3个"
                ))
                lunch.addAll(listOf(
                    "糙米饭100g+清蒸鸡胸肉80g+西兰花炒胡萝卜+冬瓜汤",
                    "藜麦饭80g+水煮虾仁100g+蒜蓉菠菜+紫菜蛋花汤",
                    "五谷饭90g+清蒸鲈鱼100g+炒豆角+番茄汤",
                    "糙米饭100g+白切鸡肉80g+清炒小白菜+海带汤"
                ))
                dinner.addAll(listOf(
                    "蒸蛋羹+清炒时蔬+小份银耳莲子汤",
                    "蔬菜沙拉（生菜+黄瓜+圣女果）+水煮蛋1个+小米粥半碗",
                    "清汤面条50g+青菜+豆腐+少许香油"
                ))
                recommendations.add("保持均衡饮食，维持当前健康体重")
                recommendations.add("每餐蔬菜占一半，蛋白质占1/4，主食占1/4")
                recommendations.add("每日饮水1500-2000ml")
            }
            "超重", "肥胖" -> {
                breakfast.addAll(listOf(
                    "燕麦片30g+无糖豆浆200ml+水煮蛋白2个+圣女果5个",
                    "全麦面包半片+牛油果1/4个+水煮蛋1个+黄瓜丝",
                    "紫薯80g+脱脂酸奶100ml+蓝莓30g",
                    "小米粥半碗+蒸蛋白2个+凉拌萝卜丝"
                ))
                lunch.addAll(listOf(
                    "糙米饭60g+清蒸鸡胸肉100g+大量蒸蔬菜+清汤",
                    "藜麦50g+水煮虾仁120g+凉拌三丝+冬瓜汤",
                    "五谷饭50g+清蒸鱼100g+白灼菜心+紫菜汤",
                    "红薯80g+白切鸡肉80g+蒸蛋羹+大拌菜"
                ))
                dinner.addAll(listOf(
                    "蔬菜沙拉（无油）+水煮蛋白2个+黄瓜汤",
                    "清汤冬瓜+蒸蛋羹+凉拌海带丝",
                    "白灼菜心+豆腐+紫菜蛋花汤（少油少盐）",
                    "蒸蛋白+清炒小白菜+银耳汤（无糖）"
                ))
                recommendations.add("控制总热量摄入，每日减少300-500卡路里")
                recommendations.add("增加蔬菜比例至每餐60%以上")
                recommendations.add("选择低GI食物，避免精制糖和白米白面")
                recommendations.add("建议配合每日30分钟中等强度运动")
            }
        }

        // 基于疾病类型的调整
        if (userInfo.diseaseType.contains("糖尿病", ignoreCase = true)) {
            recommendations.add("选择低升糖指数食物：燕麦、藜麦、绿叶蔬菜")
            recommendations.add("少食多餐，控制碳水化合物摄入量")
            recommendations.add("餐前测血糖，餐后2小时再测，记录变化")
            warnings.add("严格避免：糖果、甜饮料、蛋糕、白米粥、白面包")
            warnings.add("水果选择：苹果、梨、柚子等低糖水果，每次不超过200g")
            
            // 调整食谱 - 糖尿病专用
            breakfast.replaceAll { 
                when {
                    it.contains("粥") -> it.replace("粥", "燕麦片")
                    it.contains("面包2片") -> it.replace("面包2片", "面包1片")
                    else -> it
                }
            }
            lunch.replaceAll { 
                when {
                    it.contains("米饭150g") -> it.replace("米饭150g", "米饭80g")
                    it.contains("米饭130g") -> it.replace("米饭130g", "米饭70g")
                    it.contains("米饭120g") -> it.replace("米饭120g", "米饭60g")
                    else -> it
                }
            }
        }

        if (userInfo.diseaseType.contains("脂肪肝", ignoreCase = true)) {
            recommendations.add("减少脂肪摄入，特别是饱和脂肪和反式脂肪")
            recommendations.add("增加膳食纤维：燕麦、豆类、蔬菜每日500g以上")
            recommendations.add("适量优质蛋白质：鱼肉、鸡胸肉、豆腐")
            warnings.add("严格避免：油炸食品、肥肉、动物内脏、奶油")
            warnings.add("限制：坚果每日不超过10g，烹调油每日不超过25g")
            
            // 调整食谱 - 脂肪肝专用
            breakfast.replaceAll { it.replace("煎蛋", "水煮蛋").replace("坚果15g", "坚果5g") }
            lunch.replaceAll { it.replace("红烧", "清蒸").replace("炒", "水煮") }
            dinner.replaceAll { it.replace("煎蛋", "蒸蛋").replace("坚果", "少量坚果") }
        }

        if (userInfo.diseaseType.contains("高血压", ignoreCase = true)) {
            recommendations.add("严格控制钠盐摄入，每日不超过6g")
            recommendations.add("增加钾元素摄入：香蕉、土豆、菠菜")
            recommendations.add("多吃富含镁的食物：燕麦、坚果、深绿色蔬菜")
            warnings.add("避免：咸菜、腌制品、方便面、罐头食品")
            warnings.add("调料：少用生抽老抽，多用柠檬汁、醋、香料")
        }

        // 基于活动量的调整
        when {
            userInfo.dailyActivityLevel.contains("久坐", ignoreCase = true) -> {
                recommendations.add("适当减少碳水化合物摄入")
                recommendations.add("建议每小时起身活动5分钟")
                recommendations.add("增加日常活动：走楼梯、步行上班")
                snacks.addAll(listOf("黄瓜条", "圣女果", "无糖酸奶", "坚果5颗"))
            }
            userInfo.dailyActivityLevel.contains("运动", ignoreCase = true) -> {
                recommendations.add("运动前1小时适量补充碳水化合物")
                recommendations.add("运动后30分钟内补充蛋白质")
                recommendations.add("大量运动时注意电解质平衡")
                snacks.addAll(listOf("香蕉", "运动后蛋白粉", "坚果15g", "全麦饼干2片"))
            }
            else -> {
                snacks.addAll(listOf("苹果", "坚果（少量）", "酸奶", "胡萝卜条"))
            }
        }

        // 基于过敏史的调整
        if (userInfo.foodAllergies.contains("乳糖", ignoreCase = true)) {
            breakfast.replaceAll { it.replace("牛奶", "豆浆").replace("酸奶", "无乳糖酸奶") }
            snacks.replaceAll { it.replace("酸奶", "豆浆") }
            warnings.add("避免含乳糖的乳制品：牛奶、奶酪、冰淇淋")
            recommendations.add("钙质补充：豆制品、绿叶蔬菜、芝麻")
        }
        
        if (userInfo.foodAllergies.contains("海鲜", ignoreCase = true)) {
            lunch.replaceAll { 
                it.replace("带鱼", "鸡胸肉")
                  .replace("鲈鱼", "鸡肉")
                  .replace("三文鱼", "牛肉")
                  .replace("虾仁", "鸡丁")
            }
            warnings.add("严格避免所有海鲜类食品")
            recommendations.add("蛋白质替代：鸡肉、牛肉、豆制品、鸡蛋")
        }

        if (userInfo.foodAllergies.contains("坚果", ignoreCase = true)) {
            breakfast.replaceAll { it.replace("坚果", "葵花籽").replace("核桃", "南瓜籽") }
            dinner.replaceAll { it.replace("坚果", "瓜子").replace("杏仁", "葵花籽") }
            snacks.replaceAll { it.replace("坚果", "瓜子") }
            warnings.add("避免所有坚果类：花生、核桃、杏仁、腰果等")
        }

        // 基于饮食习惯的建议
        if (userInfo.existingDietHabits.contains("甜", ignoreCase = true)) {
            warnings.add("逐步减少甜食摄入，可用天然甜味替代")
            recommendations.add("甜味替代：蒸苹果、烤红薯、无糖酸奶加蓝莓")
            recommendations.add("戒糖过程：第一周减半，第二周再减半，第三周完全戒除")
        }
        
        if (userInfo.existingDietHabits.contains("咸", ignoreCase = true)) {
            warnings.add("减少盐分摄入，多用天然香料调味")
            recommendations.add("调味替代：柠檬汁、醋、胡椒、八角、桂皮")
            recommendations.add("逐步适应：每周减少1/4用盐量")
        }

        if (userInfo.existingDietHabits.contains("油炸", ignoreCase = true)) {
            warnings.add("避免油炸食品，改用蒸、煮、烤的烹饪方式")
            recommendations.add("健康烹饪：蒸蛋羹、水煮蔬菜、烤鸡胸肉")
        }

        if (userInfo.existingDietHabits.contains("不规律", ignoreCase = true)) {
            recommendations.add("建立规律作息：早餐7-8点，午餐12-13点，晚餐18-19点")
            recommendations.add("两餐间隔4-5小时，睡前3小时不进食")
            recommendations.add("可设置手机提醒，养成定时进餐习惯")
        }

        return DietPlan(
            breakfast = breakfast,
            lunch = lunch,
            dinner = dinner,
            snacks = snacks,
            recommendations = recommendations,
            warnings = warnings
        )
    }
}
