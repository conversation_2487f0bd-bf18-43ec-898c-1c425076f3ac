{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-44:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ffaf60804bb777caaab07cd66703845d\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,993,1078,1151,1244,1319,1394,1475,1541", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,988,1073,1146,1239,1314,1389,1470,1536,1656"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "858,953,1036,1129,1227,1316,1394,7788,7877,7962,8043,8128,8201,8294,8369,8545,8626,8692", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "948,1031,1124,1222,1311,1389,1486,7872,7957,8038,8123,8196,8289,8364,8439,8621,8687,8807"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8b176e8aba06099230d4846cfebebfa3\\transformed\\material3-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,415,531,626,722,835,973,1093,1243,1328,1431,1522,1619,1749,1869,1977,2122,2268,2398,2587,2714,2832,2954,3080,3172,3267,3395,3521,3620,3722,3834,3980,4132,4246,4346,4422,4522,4621,4731,4817,4907,5012,5092,5176,5276,5376,5471,5573,5659,5761,5859,5963,6078,6158,6258", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,109,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "168,286,410,526,621,717,830,968,1088,1238,1323,1426,1517,1614,1744,1864,1972,2117,2263,2393,2582,2709,2827,2949,3075,3167,3262,3390,3516,3615,3717,3829,3975,4127,4241,4341,4417,4517,4616,4726,4812,4902,5007,5087,5171,5271,5371,5466,5568,5654,5756,5854,5958,6073,6153,6253,6347"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1491,1609,1727,1851,1967,2062,2158,2271,2409,2529,2679,2764,2867,2958,3055,3185,3305,3413,3558,3704,3834,4023,4150,4268,4390,4516,4608,4703,4831,4957,5056,5158,5270,5416,5568,5682,5782,5858,5958,6057,6167,6253,6343,6448,6528,6612,6712,6812,6907,7009,7095,7197,7295,7399,7514,7594,7694", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,109,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "1604,1722,1846,1962,2057,2153,2266,2404,2524,2674,2759,2862,2953,3050,3180,3300,3408,3553,3699,3829,4018,4145,4263,4385,4511,4603,4698,4826,4952,5051,5153,5265,5411,5563,5677,5777,5853,5953,6052,6162,6248,6338,6443,6523,6607,6707,6807,6902,7004,7090,7192,7290,7394,7509,7589,7689,7783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee81e9003baef06be7850ff4f00325b8\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,411,512,625,731,8444", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "198,301,406,507,620,726,853,8540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fce910b49377672473080ebeba046646\\transformed\\foundation-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8812,8897", "endColumns": "84,85", "endOffsets": "8892,8978"}}]}]}