<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplication/HealthDietUI.kt"
            line="264"
            column="40"
            startOffset="9734"
            endLine="264"
            endColumn="66"
            endOffset="9760"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="18"
            column="13"
            startOffset="710"
            endLine="18"
            endColumn="45"
            endOffset="742"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.13.1">
        <fix-replace
            description="Change to 1.13.1"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.13.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="54"
            endLine="4"
            endColumn="19"
            endOffset="62"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.8.3">
        <fix-replace
            description="Change to 2.8.3"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.8.3"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="23"
            startOffset="148"
            endLine="8"
            endColumn="30"
            endOffset="155"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.8.2">
        <fix-replace
            description="Change to 1.8.2"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.8.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="19"
            startOffset="174"
            endLine="9"
            endColumn="26"
            endOffset="181"/>
    </incident>

</incidents>
